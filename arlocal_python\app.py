"""
ArLocal Python Implementation
A Flask-based reimplementation of the ArLocal Arweave local testnet
"""

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import json
import time
import random
import string
import re
from typing import Dict, List, Optional, Any
import logging
import os
from config import config
from database import ArLocalDatabase
from utils import (
    random_id, validate_txid, validate_address, calculate_price,
    save_transaction_data, load_transaction_data, delete_transaction_data,
    format_transaction_for_response, format_block_for_response
)

# Get configuration
config_name = os.environ.get('ARLOCAL_CONFIG', 'default')
app_config = config[config_name]

# Configure logging
logging.basicConfig(level=getattr(logging, app_config.LOG_LEVEL))
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(app_config)
CORS(app, origins=app_config.CORS_ORIGINS)

# Initialize database
db = ArLocalDatabase(
    db_path=app_config.DATABASE_PATH,
    data_dir=app_config.DATA_DIR
)

# Network configuration
network_config = {
    "network": app_config.NETWORK_NAME,
    "version": app_config.NETWORK_VERSION,
    "release": app_config.NETWORK_RELEASE,
    "peers": 1,
    "node_state_latency": 0
}

# In-memory logs (could be moved to database if needed)
logs = []

# Status and Info Routes
@app.route('/', methods=['GET'])
@app.route('/info', methods=['GET'])
def get_status():
    """Get network status information"""
    network_info = db.get_network_info()
    return jsonify({
        **network_config,
        **network_info
    })

@app.route('/peers', methods=['GET'])
def get_peers():
    """Get peer information"""
    host = request.headers.get('Host', 'localhost:1984')
    return jsonify([host])

@app.route('/logs', methods=['GET'])
def get_logs():
    """Get application logs"""
    try:
        # In the original, this reads from a logs file
        # For simplicity, we'll return our in-memory logs
        return '\n'.join(logs) if logs else 'No logs available'
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/reset', methods=['GET'])
def reset_network():
    """Reset the network state"""
    try:
        # Reset the network state using database
        result = db.reset_network(network_config)
        logs.append(f"Network reset at {time.time()}")
        return 'reset done'
    except Exception as e:
        logger.error(f"Error resetting network: {e}")
        return jsonify({"error": str(e)}), 500

# Transaction Anchor Route
@app.route('/tx_anchor', methods=['GET'])
def get_tx_anchor():
    """Get transaction anchor (latest block ID)"""
    latest_block = db.get_latest_block()
    return latest_block['id'] if latest_block else ''

# Price Calculation Route
@app.route('/price/<int:bytes_size>', methods=['GET'])
@app.route('/price/<int:bytes_size>/<address>', methods=['GET'])
def get_price(bytes_size: int, address: str = None):
    """Calculate transaction price based on data size"""
    price = calculate_price(bytes_size, app_config.DEFAULT_PRICE_PER_KB)
    return str(price)

# Transaction Routes
@app.route('/tx/pending', methods=['GET'])
def get_pending_transactions():
    """Get list of pending transaction IDs"""
    return jsonify(db.get_pending_transactions())

@app.route('/tx/<txid>', methods=['GET'])
def get_transaction(txid: str):
    """Get transaction by ID"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    logs.append(f"Retrieved transaction: {txid}")
    return jsonify(transaction)

@app.route('/tx/<txid>/status', methods=['GET'])
def get_transaction_status(txid: str):
    """Get transaction status"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return 'Pending'

    # Get current network height for confirmations
    network_info = db.get_network_info()
    current_height = network_info['height']

    # Return status information
    return jsonify({
        "block_indep_hash": transaction['block'],
        "block_height": transaction.get('height', 0),
        "number_of_confirmations": current_height - transaction.get('height', 0)
    })

@app.route('/tx/<txid>/offset', methods=['GET'])
def get_transaction_offset(txid: str):
    """Get transaction offset information"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    data_size = int(transaction.get('data_size', 0))

    # Simplified offset calculation
    offset = random.randint(1000, 10000)  # Mock offset

    return jsonify({
        "offset": str(offset + data_size - 1),
        "size": str(data_size)
    })

@app.route('/tx/<txid>/data', methods=['GET'])
def get_transaction_raw_data(txid: str):
    """Get raw transaction data"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Load data from file
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        return Response(data, mimetype='application/octet-stream')

    return jsonify({"status": 404, "error": "Data not found"}), 404

@app.route('/tx/<txid>/data.<ext>', methods=['GET'])
def get_transaction_data_with_extension(txid: str, ext: str):
    """Get transaction data with specific extension"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Determine content type based on extension
        content_type = 'application/octet-stream'
        if ext == 'json':
            content_type = 'application/json'
        elif ext == 'html':
            content_type = 'text/html'
        elif ext == 'txt':
            content_type = 'text/plain'

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404

# Transaction field and file routes
VALID_FIELDS = ['id', 'last_tx', 'owner', 'tags', 'target', 'quantity', 'data_root', 'data_size', 'reward', 'signature']

@app.route('/tx/<txid>/<field>', methods=['GET'])
def get_transaction_field(txid: str, field: str):
    """Get specific transaction field"""
    # Check if it's a file request (contains a dot)
    if '.' in field:
        return get_transaction_file(txid, field)

    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    if field not in VALID_FIELDS:
        return jsonify({"status": 404, "error": "Field Not Found !"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if field in transaction:
        return str(transaction[field])

    return jsonify({"status": 404, "error": "Field not found in transaction"}), 404

def get_transaction_file(txid: str, filename: str):
    """Get transaction file (helper function)"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found !"}), 404

    if 'block' not in transaction or not transaction['block']:
        return 'Pending'

    # Return the data if available
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        return Response(data, mimetype='application/octet-stream')

    return jsonify({"status": 404, "error": "File not found"}), 404

@app.route('/tx', methods=['POST'])
def post_transaction():
    """Submit a new transaction"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": 400, "error": "Invalid JSON data"}), 400

        # Generate transaction ID
        txid = random_id(43)

        # Basic validation
        required_fields = ['owner', 'target', 'quantity', 'reward', 'last_tx', 'tags', 'signature']
        for field in required_fields:
            if field not in data:
                return jsonify({"status": 400, "error": f"Missing required field: {field}"}), 400

        # Calculate reward based on data size
        data_size = int(data.get('data_size', 0))
        calculated_reward = calculate_price(data_size, app_config.DEFAULT_PRICE_PER_KB)

        # Check if owner has enough balance
        owner_address = data.get('owner', '')
        current_balance = db.get_wallet_balance(owner_address)

        if current_balance < calculated_reward:
            # Create wallet with sufficient balance if it doesn't exist
            if current_balance == 0:
                db.create_wallet(owner_address, calculated_reward * 10)
            else:
                return jsonify({"code": 410, "msg": "You don't have enough tokens"}), 410

        # Prepare transaction data
        transaction_data = {
            "id": txid,
            "owner": data['owner'],
            "target": data['target'],
            "quantity": data['quantity'],
            "reward": data['reward'],
            "last_tx": data['last_tx'],
            "tags": data['tags'],
            "signature": data['signature'],
            "data_size": data_size,
            "data_root": data.get('data_root', ''),
            "format": data.get('format', 2),
            "owner_address": owner_address
        }

        # Store transaction data if provided
        if 'data' in data and data['data']:
            save_transaction_data(db.data_dir, txid, data['data'])

        # Insert transaction into database
        if db.insert_transaction(transaction_data):
            # Deduct fee from wallet balance
            fee = max(int(data.get('reward', 0)), calculated_reward)
            new_balance = db.get_wallet_balance(owner_address) - fee
            db.update_wallet_balance(owner_address, new_balance)

            logs.append(f"Transaction submitted: {txid}")
            return txid
        else:
            return jsonify({"status": 500, "error": "Failed to insert transaction"}), 500

    except Exception as e:
        logger.error(f"Error posting transaction: {e}")
        return jsonify({"status": 500, "error": str(e)}), 500

@app.route('/tx/<txid>', methods=['DELETE'])
def delete_transaction(txid: str):
    """Delete a transaction"""
    if not validate_txid(txid):
        return jsonify({"status": 400, "error": "Invalid transaction ID format"}), 400

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not found"}), 404

    # Delete transaction from database
    if db.delete_transaction(txid):
        logs.append(f"Transaction deleted: {txid}")
        return jsonify({"status": 200, "message": "Transaction deleted"})
    else:
        return jsonify({"status": 500, "error": "Failed to delete transaction"}), 500

# Block Routes
@app.route('/block/hash/<indep_hash>', methods=['GET'])
def get_block_by_hash(indep_hash: str):
    """Get block by independent hash"""
    block = db.get_block_by_hash(indep_hash)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })

@app.route('/block/height/<int:height>', methods=['GET'])
def get_block_by_height(height: int):
    """Get block by height"""
    block = db.get_block_by_height(height)
    if not block:
        return jsonify({"status": 404, "error": "Block not found"}), 404

    return jsonify({
        "indep_hash": block['id'],
        "timestamp": block.get('timestamp', block.get('mined_at', 0) // 1000),
        "previous_block": block['previous_block'],
        "height": block['height'],
        "txs": block['txs']
    })

# Mining Routes
@app.route('/mine', methods=['GET'])
@app.route('/mine/<int:qty>', methods=['GET'])
def mine_blocks(qty: int = 1):
    """Mine blocks"""
    try:
        # Mine blocks using database
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            logs.append(f"Mined {qty} blocks: {new_block_ids}")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/mineWithFails', methods=['GET'])
@app.route('/mineWithFails/<int:qty>', methods=['GET'])
def mine_blocks_with_fails(qty: int = 1):
    """Mine blocks with failure simulation"""
    try:
        # Simulate failure rate (from config)
        fail_rate = app_config.DEFAULT_MINE_FAIL_RATE

        # Get pending transactions and simulate failures
        pending_txs = db.get_pending_transactions()
        successful_txs = []

        for txid in pending_txs:
            if random.random() >= fail_rate:  # Transaction succeeds
                successful_txs.append(txid)
            else:  # Transaction fails - remove it
                db.delete_transaction(txid)

        # For simplicity, just mine normally with successful transactions
        # In a real implementation, you'd want to modify the mine_blocks method
        # to accept a specific list of transactions
        success, new_block_ids = db.mine_blocks(qty)

        if success:
            logs.append(f"Mined {qty} blocks with {fail_rate*100}% fail rate")
            # Return updated network info
            network_info = db.get_network_info()
            return jsonify({
                **network_config,
                **network_info
            })
        else:
            return jsonify({"error": "Failed to mine blocks"}), 500

    except Exception as e:
        logger.error(f"Error mining blocks with fails: {e}")
        return jsonify({"error": str(e)}), 500

# Wallet Routes
@app.route('/wallet', methods=['POST'])
def create_wallet():
    """Create a new wallet"""
    try:
        data = request.get_json() or {}

        # Generate address if not provided
        address = data.get('address', random_id(43))

        # Validate address format
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        # Create wallet in database
        balance = float(data.get('balance', 0))
        if db.create_wallet(address, balance):
            wallet = {
                "address": address,
                "balance": balance
            }
            logs.append(f"Wallet created: {address}")
            return jsonify(wallet)
        else:
            return jsonify({"error": "Failed to create wallet"}), 500

    except Exception as e:
        logger.error(f"Error creating wallet: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/wallet/<address>/balance', methods=['GET'])
def get_wallet_balance_route(address: str):
    """Get wallet balance"""
    if not validate_address(address):
        return jsonify({"status": 422, "error": "Address badly formatted"}), 422

    balance = db.get_wallet_balance(address)
    if balance == 0.0:
        # Check if wallet exists
        wallet = db.get_wallet(address)
        if not wallet:
            return jsonify({"status": 404, "error": "Wallet not found"}), 404

    return str(balance)

@app.route('/wallet/<address>/balance', methods=['PATCH'])
def update_wallet_balance_route(address: str):
    """Update wallet balance"""
    try:
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        data = request.get_json()
        if not data or 'balance' not in data:
            return jsonify({"status": 422, "error": "Balance is required !"}), 422

        balance = float(data['balance'])
        if db.update_wallet_balance(address, balance):
            logs.append(f"Wallet balance updated: {address} -> {balance}")
            return jsonify(data)
        else:
            return jsonify({"error": "Failed to update wallet balance"}), 500

    except Exception as e:
        logger.error(f"Error updating wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/mint/<address>/<int:balance>', methods=['GET'])
def add_wallet_balance(address: str, balance: int):
    """Add balance to wallet (mint tokens)"""
    try:
        if not validate_address(address):
            return jsonify({"status": 422, "error": "Address badly formatted"}), 422

        new_balance = db.increment_wallet_balance(address, float(balance))
        logs.append(f"Minted {balance} tokens for {address}")
        return str(new_balance)

    except Exception as e:
        logger.error(f"Error adding wallet balance: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/wallet/<address>/last_tx', methods=['GET'])
def get_wallet_last_transaction(address: str):
    """Get last transaction for wallet"""
    if not validate_address(address):
        return jsonify({"status": 422, "error": "Address badly formatted"}), 422

    last_tx = db.get_wallet_last_transaction(address)
    return last_tx or ''

# Chunk Routes
@app.route('/chunk', methods=['POST'])
def post_chunk():
    """Submit chunk data"""
    try:
        data = request.get_json()
        if not data or 'chunk' not in data:
            return jsonify({"status": 400, "error": "Invalid chunk data"}), 400

        # Calculate offset using database
        offset = db.get_next_chunk_offset()

        # Store chunk
        chunk_data = {
            'chunk': data['chunk'],
            'data_root': data.get('data_root', ''),
            'data_size': data.get('data_size', 0),
            'offset': offset,
            'data_path': data.get('data_path', '')
        }

        if db.insert_chunk(chunk_data):
            logs.append(f"Chunk stored at offset: {offset}")
            return jsonify({})
        else:
            return jsonify({"error": "Failed to store chunk"}), 500

    except Exception as e:
        logger.error(f"Error posting chunk: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/chunk/<int:offset>', methods=['GET'])
def get_chunk_by_offset_route(offset: int):
    """Get chunk by offset"""
    chunk = db.get_chunk_by_offset(offset)
    if not chunk:
        return '', 204  # No Content

    return jsonify(chunk)

# Data Routes (for transaction data access)
@app.route('/<txid>', methods=['GET', 'HEAD'])
@app.route('/<txid>/<path:subpath>', methods=['GET', 'HEAD'])
def get_data_route(txid: str, subpath: str = None):
    """Get transaction data by transaction ID"""
    if not validate_txid(txid):
        return jsonify({"status": 404, "error": "Not Found"}), 404

    transaction = db.get_transaction(txid)
    if not transaction:
        return jsonify({"status": 404, "error": "Not Found"}), 404

    if request.method == 'HEAD':
        # Return headers only
        response = Response()
        response.headers['Content-Length'] = str(transaction.get('data_size', 0))

        # Check if data file exists
        data = load_transaction_data(db.data_dir, txid)
        if data is not None:
            response.headers['Content-Type'] = 'application/octet-stream'
        return response

    # GET request - return data
    data = load_transaction_data(db.data_dir, txid)
    if data is not None:
        # Determine content type from transaction tags
        content_type = 'application/octet-stream'
        if 'tags' in transaction and transaction['tags']:
            tags = transaction['tags']
            for tag in tags:
                if tag.get('name') == 'Content-Type':
                    content_type = tag.get('value', content_type)
                    break

        return Response(data, mimetype=content_type)

    return jsonify({"status": 404, "error": "Data not found"}), 404

# GraphQL placeholder route
@app.route('/graphql', methods=['POST', 'GET'])
def graphql_endpoint():
    """GraphQL endpoint placeholder"""
    if request.method == 'GET':
        # Return GraphQL playground or schema
        return jsonify({
            "message": "GraphQL endpoint - not fully implemented in this simple version",
            "available_queries": ["transaction", "transactions", "block", "blocks"]
        })

    # POST request - handle GraphQL queries
    try:
        data = request.get_json()
        query = data.get('query', '')

        # Very basic GraphQL query handling
        if 'transaction' in query:
            return jsonify({
                "data": {
                    "transaction": None,
                    "message": "GraphQL transaction queries not implemented in this simple version"
                }
            })
        elif 'block' in query:
            return jsonify({
                "data": {
                    "block": None,
                    "message": "GraphQL block queries not implemented in this simple version"
                }
            })
        else:
            return jsonify({
                "errors": [{"message": "Query not supported in this simple implementation"}]
            })

    except Exception as e:
        return jsonify({"errors": [{"message": str(e)}]}), 400

# Catch-all route for unmatched requests
@app.route('/<path:other>', methods=['GET'])
def catch_all(other: str):
    """Catch-all route for unmatched requests"""
    return jsonify({
        "status": 400,
        "error": "Request type not found."
    }), 400

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({"status": 404, "error": "Not Found"}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({"status": 500, "error": "Internal Server Error"}), 500

if __name__ == '__main__':
    print(f"Starting ArLocal Python server on {app_config.HOST}:{app_config.PORT}...")
    print(f"Network: {app_config.NETWORK_NAME}")
    print(f"Debug mode: {app_config.DEBUG}")
    print("Available endpoints:")
    print("  GET  /                    - Network status")
    print("  GET  /info               - Network info")
    print("  GET  /peers              - Peer list")
    print("  GET  /logs               - Application logs")
    print("  GET  /reset              - Reset network")
    print("  GET  /tx_anchor          - Transaction anchor")
    print("  GET  /price/<bytes>      - Calculate price")
    print("  GET  /tx/pending         - Pending transactions")
    print("  GET  /tx/<txid>          - Get transaction")
    print("  POST /tx                 - Submit transaction")
    print("  GET  /mine/<qty>         - Mine blocks")
    print("  POST /wallet             - Create wallet")
    print("  GET  /wallet/<addr>/balance - Get balance")
    print("  GET  /mint/<addr>/<amt>  - Mint tokens")
    print("  POST /chunk              - Submit chunk")
    print("  GET  /chunk/<offset>     - Get chunk")
    print("  POST /graphql            - GraphQL endpoint")
    print()

    app.run(host=app_config.HOST, port=app_config.PORT, debug=app_config.DEBUG)
